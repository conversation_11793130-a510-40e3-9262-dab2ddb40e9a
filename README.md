# BladeRF Video Processing - Node.js C++ Addon

A Node.js C++ addon for interfacing with BladeRF Software Defined Radio (SDR) devices, designed for video processing applications.

**Status**: ✅ Successfully built and tested on macOS with libbladeRF 2.5.0
**ARM Support**: ✅ Optimized for Raspberry Pi 5 (ARM64/AArch64)

## Features

- **Hello World C++ Addon**: Basic C++ addon functionality with Node.js integration
- **BladeRF SDK Integration**: Full integration with libbladeRF for device control
- **Device Management**: Enumerate, open, and close BladeRF devices
- **Configuration Control**: Set frequency, sample rate, bandwidth, and gain
- **Device Information**: Retrieve device serial, firmware, and FPGA versions
- **IQ Data Recording**: Record IQ samples to WAV files with configurable duration
- **Video Frame Processing**: Integrated JPEG export for composite video signal visualization
- **C++17 Support**: Modern C++ standards with proper error handling

## Prerequisites

### System Requirements
- Node.js 14.0.0 or higher
- C++ compiler with C++17 support
- BladeRF SDK (libbladeRF) installed
- libjpeg-turbo (for video frame visualization)

### Supported Platforms
- ✅ **macOS** (Intel/Apple Silicon)
- ✅ **Linux x86_64**
- ✅ **Linux ARM64** (Raspberry Pi 4/5, other ARM64 systems)
- ✅ **Linux ARM** (Raspberry Pi 3, other 32-bit ARM systems)

### Installing BladeRF SDK

#### Ubuntu/Debian
```bash
# Easy installation via PPA (Ubuntu 20.04+)
sudo add-apt-repository ppa:nuandllc/bladerf
sudo apt-get update
sudo apt-get install bladerf libbladerf-dev

# Install libjpeg-turbo for video processing
sudo apt-get install libturbojpeg0-dev

# Or install dependencies and build from source
sudo apt-get install libusb-1.0-0-dev libusb-1.0-0 build-essential cmake libncurses5-dev libedit-dev pkg-config git wget libcurl4-openssl-dev
```

#### macOS
```bash
# Using Homebrew (if available)
brew install bladerf

# Install libjpeg-turbo for video processing
brew install jpeg-turbo

# Or build from source following the BladeRF documentation
```

#### Raspberry Pi / ARM Linux
```bash
# Quick installation (recommended)
chmod +x install-rpi.sh
./install-rpi.sh
```

#### Manual Installation - Debian/Ubuntu ARM
```bash
# Install dependencies
sudo apt-get update
sudo apt-get install -y build-essential cmake git pkg-config \
    libusb-1.0-0-dev libusb-1.0-0 libncurses5-dev libedit-dev

# Install BladeRF (try package first)
sudo apt-get install -y libbladerf2 libbladerf-dev bladerf

# If package not available, build from source
git clone https://github.com/Nuand/bladeRF.git
cd bladeRF/host
mkdir build && cd build
cmake -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/usr/local -DINSTALL_UDEV_RULES=ON ../
make -j$(nproc) && sudo make install && sudo ldconfig

# Add user to plugdev group for device access
sudo usermod -a -G plugdev $USER
```

#### From Source (Other Platforms)
```bash
git clone https://github.com/Nuand/bladeRF.git
cd bladeRF/host
mkdir build && cd build
cmake -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/usr/local -DINSTALL_UDEV_RULES=ON ../
make && sudo make install && sudo ldconfig
```

## Installation

### Quick Start - Raspberry Pi 5
```bash
git clone <repository-url>
cd bladerf-video
chmod +x install-rpi.sh
./install-rpi.sh
```

### Manual Installation

1. **Clone or initialize the project**:
```bash
git clone <repository-url>
cd bladerf-video
```

2. **Install Node.js dependencies**:
```bash
npm install
```

3. **Build the C++ addon**:
```bash
npm run build
```

### ARM-Specific Build Options

The build system automatically detects ARM architecture and applies optimizations:

- **Raspberry Pi 5**: Cortex-A76 optimizations with NEON SIMD
- **Raspberry Pi 4**: Cortex-A72 optimizations
- **Raspberry Pi 3**: Cortex-A53 optimizations

#### Cross-Compilation (Development)

**Option 1: macOS with existing tools**
```bash
# Uses clang or existing aarch64-elf-gcc
npm run cross-compile-macos -- --rpi 5
```

**Option 2: Docker (most reliable)**
```bash
# Works on macOS, Linux, Windows
npm run cross-compile-docker -- --rpi 5
```

**Option 3: Linux with proper toolchains**
```bash
# Install cross-compilation tools (Ubuntu/Debian)
sudo apt-get install gcc-aarch64-linux-gnu g++-aarch64-linux-gnu  # For ARM64
sudo apt-get install gcc-arm-linux-gnueabihf g++-arm-linux-gnueabihf  # For ARM32

# Cross-compile for Raspberry Pi 5 (ARM64)
npm run cross-compile -- --rpi 5

# Cross-compile for Raspberry Pi 4 (ARM64)
npm run cross-compile -- --rpi 4

# Cross-compile for Raspberry Pi 3 (ARM32)
npm run cross-compile -- --rpi 3 --arch arm
```

#### Manual Build Configuration

For custom ARM targets, you can override the CPU type:
```bash
export CC_target_arch=arm64
export CXX_target_arch=arm64
npm run build
```

## Usage

### Basic Example

```javascript
const BladeRF = require('./index.js');

// Create BladeRF instance
const bladerf = new BladeRF();

// Test basic functionality
console.log(bladerf.helloWorld());
console.log('Library version:', bladerf.getLibraryVersion());

// List available devices
const devices = bladerf.getDeviceList();
console.log('Available devices:', devices);

// Open device (first available or specific device)
if (bladerf.openDevice()) {
    console.log('Device opened successfully');
    
    // Configure device
    const success = bladerf.configure({
        frequency: 915000000,    // 915 MHz
        sampleRate: 1000000,     // 1 MHz
        bandwidth: 1500000,      // 1.5 MHz
        gain: 30                 // 30 dB
    });
    
    if (success) {
        console.log('Device configured successfully');
        
        // Get device information
        const info = bladerf.getDeviceInfo();
        console.log('Device info:', info);
        
        // Perform basic test
        const testResult = bladerf.performBasicTest();
        console.log('Basic test result:', testResult);

        // Record IQ data to WAV file
        console.log('Starting IQ recording...');
        const recordingResult = await bladerf.recordIQ('iq_samples.wav', 5); // 5 seconds
        console.log('Recording completed:', recordingResult);

        // Or manual recording control
        bladerf.startIQRecording('manual_iq.wav', 10); // Max 10 seconds
        setTimeout(() => {
            bladerf.stopIQRecording();
            console.log('Manual recording stopped');
        }, 3000); // Stop after 3 seconds
    }

    // Close device
    bladerf.closeDevice();
}
```

### API Reference

#### Device Management
- `getLibraryVersion()` - Get libbladeRF version
- `getDeviceList()` - Get list of available BladeRF devices
- `openDevice(deviceId?)` - Open device (optional device ID)
- `closeDevice()` - Close current device
- `isDeviceOpen()` - Check if device is open

#### Configuration
- `setFrequency(hz)` - Set frequency in Hz
- `setSampleRate(hz)` - Set sample rate in Hz
- `setBandwidth(hz)` - Set bandwidth in Hz
- `setGain(db)` - Set gain in dB
- `configure(config)` - Configure multiple parameters at once

#### Information
- `getCurrentConfig()` - Get current device configuration
- `getDeviceSerial()` - Get device serial number
- `getFirmwareVersion()` - Get firmware version
- `getFPGAVersion()` - Get FPGA version
- `getDeviceInfo()` - Get comprehensive device information

#### Testing
- `helloWorld()` - Test basic C++ addon functionality
- `performBasicTest()` - Perform BladeRF configuration test

#### Platform Detection
- `getPlatformInfo()` - Get comprehensive platform information
- `getOptimalThreadCount()` - Get recommended thread count for this platform

#### IQ Data Recording
- `startIQRecording(filename, maxDurationSeconds)` - Start recording IQ data to WAV file
- `stopIQRecording()` - Stop current recording
- `isRecording()` - Check if currently recording
- `getRecordingDuration()` - Get current recording duration in seconds
- `getRecordedSamples()` - Get number of recorded samples
- `recordIQ(filename, durationSeconds)` - Convenience method for timed recording

## Testing

Run the comprehensive test suite:

```bash
npm test
```

Run the interactive example:

```bash
npm run example
```

Test IQ recording functionality:

```bash
node test-iq-recording.js
```

The test will:
1. Test basic C++ addon functionality
2. Check libbladeRF library version
3. Enumerate available devices
4. Open and configure a device (if available)
5. Perform basic BladeRF operations
6. Clean up and close the device

## Project Structure

```
bladerf-video/
├── src/
│   ├── addon.cpp           # Main Node.js C++ addon
│   ├── bladerf_wrapper.h   # BladeRF wrapper header
│   └── bladerf_wrapper.cpp # BladeRF wrapper implementation
├── binding.gyp            # Node-gyp build configuration
├── package.json           # npm configuration
├── index.js               # JavaScript API wrapper
├── test.js                # Test suite
└── README.md              # This file
```

## Build Configuration

The project uses `node-gyp` with the following key configurations:

- **C++ Standard**: C++17
- **Libraries**: libbladeRF
- **Include Paths**: `/usr/local/include`, `/usr/include`
- **Library Paths**: `/usr/local/lib`, `/usr/lib`

### ARM Optimizations

The addon includes specific optimizations for ARM architectures:

#### Raspberry Pi Optimizations
- **Pi 5**: Cortex-A76 CPU targeting with `-mcpu=cortex-a76 -mtune=cortex-a76`
- **Pi 4**: Cortex-A72 CPU targeting with `-mcpu=cortex-a72 -mtune=cortex-a72`
- **Pi 3**: Cortex-A53 CPU targeting with `-mcpu=cortex-a53 -mtune=cortex-a53`
- **NEON SIMD**: Automatic detection and utilization of ARM NEON instructions
- **Thread Optimization**: Platform-aware thread count recommendations

#### Performance Features
- **Fast Math**: `-ffast-math` optimizations for floating-point operations
- **Loop Unrolling**: `-funroll-loops` for better performance
- **Architecture Detection**: Runtime platform detection for optimal configuration
- **Thermal Awareness**: Conservative thread limits to prevent thermal throttling

## IQ Data Recording

The addon supports recording IQ (In-phase/Quadrature) data directly to WAV files for signal analysis and processing.

### WAV File Format
- **Channels**: 2 (I channel = left, Q channel = right)
- **Sample Format**: 16-bit signed integers (int16)
- **Sample Rate**: Matches BladeRF configuration (e.g., 1 MHz, 2 MHz)
- **Endianness**: Little-endian (standard WAV format)
- **Compatibility**: Standard WAV format, playable in audio software

### Recording Methods

#### Automatic Recording
```javascript
// Record for specific duration (returns Promise)
const result = await bladerf.recordIQ('signal.wav', 10); // 10 seconds
console.log(`Recorded ${result.samples} samples in ${result.duration}s`);
```

#### Manual Recording Control
```javascript
// Start recording with maximum duration
bladerf.startIQRecording('signal.wav', 60); // Max 60 seconds

// Monitor progress
setInterval(() => {
    if (bladerf.isRecording()) {
        console.log(`Recording: ${bladerf.getRecordingDuration()}s`);
    }
}, 1000);

// Stop manually
setTimeout(() => {
    bladerf.stopIQRecording();
}, 5000); // Stop after 5 seconds
```

### Use Cases
- **GNU Radio**: Import WAV files for signal processing
- **MATLAB/Octave**: Load as audio for DSP analysis
- **Python**: Use scipy.io.wavfile or librosa for processing
- **Audacity**: Visualize I/Q data as stereo audio
- **Custom DSP**: Process with any WAV-compatible library

## Video Frame Processing

The system includes integrated video frame processing capabilities for composite video signals, with automatic JPEG export for visualization and analysis.

### Features

- **Self-contained FrameCanvas** with integrated JPEG export
- **High-quality JPEG export** using libjpeg-turbo (2-6x faster than standard libjpeg)
- **Grayscale conversion** with configurable black/white levels
- **Cross-platform compatibility** (macOS, Linux)
- **Professional-grade image quality** for video analysis
- **Optimized performance** with pre-allocated buffers and persistent TurboJPEG compressor

### Automatic Integration

The FrameCompositionNode automatically exports JPEG files when video frames are complete:

```cpp
// This happens automatically in the video processing pipeline
frameCanvas_->render("frame_000001_NTSC_FULL.jpg", equalizationBlankLevel_);
```

### File Naming Convention

The system automatically generates descriptive filenames:
- `frame_000001_NTSC_FULL.jpg` - Complete NTSC frame
- `frame_000002_PAL_ODD.jpg` - PAL frame with only odd field
- `frame_000003_SECAM_EVEN.jpg` - SECAM frame with only even field

### Scaling Algorithm

Values are scaled from `[blackLevel, whiteLevel]` to `[0, 255]`:
- Values ≤ blackLevel → 0 (black)
- Values ≥ whiteLevel → 255 (white)
- Values in between → Linear interpolation

### Performance Optimizations

- **Pre-allocated buffers**: Grayscale and JPEG buffers allocated once during construction
- **Persistent TurboJPEG compressor**: Initialized once, reused for all exports
- **Zero-copy operations**: Direct buffer reuse eliminates memory allocation overhead
- **SIMD optimizations**: Automatic vectorization on supported platforms

## Troubleshooting

### Build Issues

1. **Missing libbladeRF**: Ensure BladeRF SDK is properly installed
2. **Permission Issues**: Make sure udev rules are installed for BladeRF
3. **C++ Compiler**: Ensure you have a C++14 compatible compiler

### Runtime Issues

1. **Device Not Found**: Check if BladeRF is connected and recognized by the system
2. **Permission Denied**: Ensure your user is in the appropriate group (usually `plugdev`)
3. **Firmware Issues**: Some operations require proper firmware/FPGA images loaded

### Common Commands

```bash
# Rebuild the addon
npm run clean && npm run build

# Check for BladeRF devices
bladeRF-cli -p

# Load firmware (if needed)
bladeRF-cli --flash-firmware /path/to/firmware.img
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

MIT License - see LICENSE file for details.

## References

- [BladeRF Official Documentation](https://github.com/Nuand/bladeRF/wiki)
- [libbladeRF API Documentation](https://www.nuand.com/libbladeRF-doc/)
- [Node.js C++ Addons Documentation](https://nodejs.org/api/addons.html)
- [Node-gyp Documentation](https://github.com/nodejs/node-gyp)
