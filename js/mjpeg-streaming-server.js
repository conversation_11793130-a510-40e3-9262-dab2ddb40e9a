#!/usr/bin/env node

/**
 * MJPEG Streaming Server with BladeRF Video Processor Integration
 * 
 * This server demonstrates real-time JPEG streaming over HTTP using the
 * BladeRF addon's video processor with configuration object pattern.
 * 
 * Usage: node js/mjpeg-streaming-server.js
 * Then open: http://localhost:3010 in a web browser
 */

const http = require('http');
const path = require('path');

// Load the BladeRF addon
let addon;
try {
    // Try Debug build first, then Release build
    try {
        addon = require('../build/Debug/bladerf_addon.node');
        console.log('✅ BladeRF addon loaded successfully (Debug)');
    } catch (debugError) {
        addon = require('../build/Release/bladerf_addon.node');
        console.log('✅ BladeRF addon loaded successfully (Release)');
    }
} catch (error) {
    console.error('❌ Failed to load BladeRF addon:', error.message);
    process.exit(1);
}

// Server configuration
const SERVER_PORT = 3010;
const SAMPLE_FILE = path.join(__dirname, '..', 'samples', 'recording.wav');

// MJPEG streaming boundary
const BOUNDARY = 'mjpegboundary';

// Global video processor and client management
let globalVideoProcessor = null;
let globalFrameCount = 0;
const connectedClients = new Set();

/**
 * Creates a global video processor instance (singleton pattern)
 * @returns {Object} Video processor instance
 */
function createGlobalVideoProcessor() {
    if (globalVideoProcessor) {
        console.log('🔄 Video processor already exists');
        return globalVideoProcessor;
    }

    console.log('🔧 Creating global video processor...');

    const config = {
        settings: "demo", // Use same setting as working debug script

        onEvent: (eventName) => {
            console.log(`📡 Global processor event: ${eventName}`);
        },

        onFrame: (frameData) => {
            globalFrameCount++;
            console.log(`🎬 Global frame #${globalFrameCount} received (frame ${frameData?.frameNumber || 'unknown'})`);

            // Broadcast to all connected clients
            broadcastFrameToClients(frameData);
        },

        onError: (error) => {
            console.error('🚨 Global processor error:', error);
            // Notify all clients of error
            for (const client of connectedClients) {
                if (!client.destroyed) {
                    client.end();
                }
            }
            connectedClients.clear();
        },

        onStop: (reason) => {
            console.log(`🛑 Global processor stopped (reason: ${reason})`);
        }
    };

    try {
        globalVideoProcessor = addon.createIQVideoProcessor(config);
        console.log('✅ Global video processor created');
        return globalVideoProcessor;
    } catch (error) {
        console.error('❌ Failed to create global video processor:', error.message);
        throw error;
    }
}

/**
 * Broadcasts a frame to all connected clients
 * @param {Object} frameData - Frame data from video processor
 */
function broadcastFrameToClients(frameData) {
    if (connectedClients.size === 0) {
        return;
    }

    try {
        // Extract buffer from frame data object
        const buffer = Buffer.from(frameData.image);

        console.log(`📡 Broadcasting frame to ${connectedClients.size} clients (${buffer.length} bytes)`);

        // Send to all connected clients
        for (const client of connectedClients) {
            try {
                if (!client.destroyed) {
                    client.write(`--${BOUNDARY}\r\n`);
                    client.write('Content-Type: image/jpeg\r\n');
                    client.write(`Content-Length: ${buffer.length}\r\n\r\n`);
                    client.write(buffer);
                    client.write('\r\n');
                }
            } catch (error) {
                console.error('❌ Error sending frame to client:', error.message);
                connectedClients.delete(client);
            }
        }
    } catch (error) {
        console.error('❌ Error processing frame for broadcast:', error.message);
    }
}

/**
 * Handles individual client connections for MJPEG streaming
 * @param {http.IncomingMessage} req - HTTP request
 * @param {http.ServerResponse} res - HTTP response
 */
function handleClientConnection(req, res) {
    console.log(`🔗 Client connected from ${req.connection.remoteAddress}`);

    // Set MJPEG streaming headers
    res.writeHead(200, {
        'Content-Type': `multipart/x-mixed-replace; boundary=${BOUNDARY}`,
        'Cache-Control': 'no-cache',
        'Connection': 'close',
        'Pragma': 'no-cache'
    });

    // Add client to broadcast list
    connectedClients.add(res);
    console.log(`📊 Total connected clients: ${connectedClients.size}`);

    // Create global video processor if it doesn't exist
    try {
        createGlobalVideoProcessor();
    } catch (error) {
        console.error('❌ Failed to start video processing:', error.message);
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Internal Server Error: Failed to initialize video processor');
        connectedClients.delete(res);
        return;
    }

    // Handle client disconnect
    req.on('close', () => {
        console.log(`🔌 Client disconnected`);
        connectedClients.delete(res);
        console.log(`📊 Remaining connected clients: ${connectedClients.size}`);

        // If no more clients, we could stop the processor (but keeping it running for demo)
        if (connectedClients.size === 0) {
            console.log('📴 No more clients, keeping processor running for next connection');
        }
    });

    req.on('error', (error) => {
        console.error('❌ Client connection error:', error.message);
        connectedClients.delete(res);
    });
}

/**
 * Creates and starts the HTTP server
 */
function startServer() {
    const server = http.createServer((req, res) => {
        console.log(`📥 Request: ${req.method} ${req.url}`);

        if (req.url === '/stream') {
            // Handle MJPEG streaming
            handleClientConnection(req, res);
        } else if (req.url === '/') {
            // Serve HTML page with embedded video player
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>BladeRF MJPEG Stream</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; margin: 50px; }
                        img { border: 2px solid #333; max-width: 90%; height: auto; }
                        .info { margin: 20px 0; color: #666; }
                    </style>
                </head>
                <body>
                    <h1>BladeRF Video Stream</h1>
                    <div class="info">Real-time MJPEG streaming from BladeRF IQ data</div>
                    <img src="/stream" alt="Video Stream" />
                    <div class="info">
                        <p>Stream URL: <code>http://localhost:${SERVER_PORT}/stream</code></p>
                        <p>Source: ${SAMPLE_FILE}</p>
                    </div>
                </body>
                </html>
            `);
        }
    });

    server.on('error', (error) => {
        console.error('❌ Server error:', error.message);
        process.exit(1);
    });

    server.listen(SERVER_PORT, () => {
        console.log(`🚀 MJPEG streaming server started on port ${SERVER_PORT}`);
        console.log(`📺 Open http://localhost:${SERVER_PORT} in your browser`);
        console.log(`🎯 Direct stream: http://localhost:${SERVER_PORT}/stream`);
        console.log(`📁 Using sample file: ${SAMPLE_FILE}`);
        console.log('');
        console.log('Press Ctrl+C to stop the server');
    });

    // Graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down server...');
        server.close(() => {
            console.log('✅ Server stopped');
            process.exit(0);
        });
    });
}

// Verify sample file exists
const fs = require('fs');
if (!fs.existsSync(SAMPLE_FILE)) {
    console.error(`❌ Sample file not found: ${SAMPLE_FILE}`);
    console.error('Please ensure the sample file exists before starting the server');
    process.exit(1);
}

// Start the server
startServer();
