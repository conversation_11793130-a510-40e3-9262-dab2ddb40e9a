#!/usr/bin/env node

/**
 * Minimal Video Processor Test
 *
 * This example demonstrates the basic functionality of the minimal video processor
 * implementation without complex parameter handling.
 */

const addon = require('../build/Release/bladerf_addon');

console.log('🚀 Minimal Video Processor Test\n');

async function runTest() {
try {
    // 1. Test createIQVideoProcessor function with callbacks
    console.log('1. Testing createIQVideoProcessor function with callbacks:');
    console.log('   Creating VideoProcessor with WAV stream and callback functions...');

    // Define callback functions
    const onReady = (value) => {
        console.log(`   📡 onReady callback invoked with value: ${value}`);
    };

    const onFrame = (value) => {
        console.log(`   🎬 onFrame callback invoked with value: ${value}`);
    };

    const onError = (value) => {
        console.log(`   ❌ onError callback invoked with value: ${value}`);
    };

    const videoProcessor = addon.createIQVideoProcessor(onReady, onFrame, onError);

    if (videoProcessor && typeof videoProcessor === 'object') {
        console.log('   ✅ VideoProcessor instance created successfully');
        console.log('   📁 Using samples/recording.wav as data source');
        console.log('   🔄 Loop mode enabled with timing simulation');
        console.log('   📦 Returned object type:', typeof videoProcessor);
        console.log('   🔧 Available methods:', Object.getOwnPropertyNames(videoProcessor.__proto__));
    } else {
        console.log('   ❌ Failed to create VideoProcessor');
    }

    console.log('\n2. Testing VideoProcessor stop method:');
    if (videoProcessor && typeof videoProcessor.stop === 'function') {
        const stopResult = videoProcessor.stop();

        if (stopResult) {
            console.log('   ✅ VideoProcessor stopped successfully');
        } else {
            console.log('   ⚠️  VideoProcessor stop returned false');
        }
    } else {
        console.log('   ❌ VideoProcessor instance does not have stop method');
    }

    // 3. Test creating multiple instances with callbacks
    console.log('\n3. Testing multiple VideoProcessor instances with callbacks:');
    try {
        // Create callback functions for first instance
        const onReady1 = (value) => console.log(`   📡 Instance 1 onReady: ${value}`);
        const onFrame1 = (value) => console.log(`   🎬 Instance 1 onFrame: ${value}`);
        const onError1 = (value) => console.log(`   ❌ Instance 1 onError: ${value}`);

        // Create callback functions for second instance
        const onReady2 = (value) => console.log(`   📡 Instance 2 onReady: ${value}`);
        const onFrame2 = (value) => console.log(`   🎬 Instance 2 onFrame: ${value}`);
        const onError2 = (value) => console.log(`   ❌ Instance 2 onError: ${value}`);

        const processor1 = addon.createIQVideoProcessor(onReady1, onFrame1, onError1);
        const processor2 = addon.createIQVideoProcessor(onReady2, onFrame2, onError2);

        if (processor1 && processor2) {
            console.log('   ✅ Multiple instances created successfully');
            console.log('   🔄 Each instance manages its own VideoProcessor and callbacks');

            // Wait a moment to see some callbacks
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Stop both instances
            processor1.stop();
            processor2.stop();
            console.log('   ✅ Both instances stopped successfully');
        } else {
            console.log('   ❌ Failed to create multiple instances');
        }
    } catch (multiError) {
        console.log('   ⚠️  Multiple instance test failed:', multiError.message);
    }

} catch (error) {
    console.error('❌ Error during testing:', error.message);
    process.exit(1);
}
}

// Run the test
runTest().then(() => {
    console.log('\n🎉 Callback-enhanced video processor test completed!');
    console.log('📝 The enhanced C++ integration with callback support is working correctly.');
    console.log('🔧 Each JavaScript object now owns and manages its own VideoProcessor instance with callbacks.');
}).catch(error => {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
});
