#!/usr/bin/env node

/**
 * Debug Test for BladeRF Video Processor
 * 
 * This script is specifically designed for debugging the debug build
 * and can be used with Node.js inspector.
 */

console.log('🐛 Debug Test for BladeRF Video Processor\n');

try {
    // Load the debug build
    const addon = require('../build/Debug/bladerf_addon.node');
    
    console.log('✅ Debug addon loaded successfully');
    console.log('📋 Available functions:', Object.keys(addon));
    
    // Test basic functionality with debugging breakpoints
    console.log('\n🧪 Testing basic functionality:');
    
    // Test createIQVideoProcessor function with callbacks - good place for a breakpoint
    if (typeof addon.createIQVideoProcessor === 'function') {
        console.log('   Testing createIQVideoProcessor with callbacks...');
        try {
            // Define simple callback functions for debugging
            const onReady = (value) => console.log(`   🔍 DEBUG onReady: ${value}`);
            const onFrame = (value) => console.log(`   🔍 DEBUG onFrame: ${value}`);
            const onError = (value) => console.log(`   🔍 DEBUG onError: ${value}`);

            const videoProcessor = addon.createIQVideoProcessor(onReady, onFrame, onError);
            if (videoProcessor && typeof videoProcessor === 'object') {
                console.log('   ✅ VideoProcessor instance created successfully');
                console.log('   📦 Returned object type:', typeof videoProcessor);

                // Wait a moment to see callbacks
                setTimeout(() => {
                    // Test stop method
                    if (typeof videoProcessor.stop === 'function') {
                        const stopResult = videoProcessor.stop();
                        console.log('   ✅ Stop method called, result:', stopResult);
                    } else {
                        console.log('   ⚠️  Stop method not available');
                    }
                }, 500);
            } else {
                console.log('   ❌ Failed to create VideoProcessor instance');
            }
        } catch (vpError) {
            console.log('   ⚠️  VideoProcessor test failed:', vpError.message);
        }
    } else {
        console.log('   ⚠️  createIQVideoProcessor function not available');
    }

    console.log('\n   Build configuration is working correctly!');
    
    console.log('\n✅ Debug test completed successfully!');
    console.log('🔍 You can set breakpoints in the C++ code and debug with your IDE');
    
} catch (error) {
    console.error('❌ Error in debug test:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
}
