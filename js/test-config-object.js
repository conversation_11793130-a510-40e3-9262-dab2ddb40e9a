#!/usr/bin/env node

/**
 * Test script for the refactored createIQVideoProcessor function
 * Tests the new configuration object approach with all callbacks
 */

const addon = require('../build/Debug/bladerf_addon');

// Callback statistics tracking
const callbackStats = {
    onEventCalled: false,
    onStopCalled: false,
    onErrorCalled: false,
    onFrameCalled: false,
    frameCount: 0,
    startTime: Date.now()
};

async function runConfigObjectTest() {
    try {
        console.log('🧪 Testing refactored createIQVideoProcessor with configuration object:');
        console.log('   📁 Using samples/recording.wav as data source');

        // Create configuration object with all required callbacks
        const config = {
            settings: "demo", // Placeholder for future use
            
            onEvent: (eventName) => {
                callbackStats.onEventCalled = true;
                console.log(`   📡 ✅ onEvent callback invoked! Event: ${eventName}`);
            },
            
            onStop: (reason) => {
                callbackStats.onStopCalled = true;
                console.log(`   🛑 ✅ onStop callback invoked! Reason: ${reason}`);
            },
            
            onError: (error) => {
                callbackStats.onErrorCalled = true;
                console.log(`   ❌ ✅ onError callback invoked! Error: ${error}`);
            },
            
            onFrame: (frame) => {
                callbackStats.onFrameCalled = true;
                callbackStats.frameCount++;
                const elapsed = Date.now() - callbackStats.startTime;

                console.log(`   🎬 ✅ onFrame callback #${callbackStats.frameCount} invoked! (${elapsed}ms elapsed)`);
                console.log(`      📊 Frame details:`);
                console.log(`         - Frame number: ${frame.frameNumber}`);
                console.log(`         - Dimensions: ${frame.width}x${frame.height}`);
                console.log(`         - Data size: ${frame.image.byteLength} bytes`);
                console.log(`         - Buffer type: ${frame.image.constructor.name}`);

                // Stop after receiving a few frames for testing
                if (callbackStats.frameCount >= 3) {
                    console.log(`\n   🛑 Stopping after ${callbackStats.frameCount} frames for testing purposes`);
                    setTimeout(() => {
                        if (videoProcessor) {
                            videoProcessor.stop();
                        }
                    }, 100);
                }
            }
        };

        // Create the VideoProcessor instance with configuration object
        console.log('   🔧 Creating VideoProcessor instance with config object...');
        const videoProcessor = addon.createIQVideoProcessor(config);

        if (videoProcessor && typeof videoProcessor === 'object') {
            console.log('   ✅ VideoProcessor instance created successfully');
            console.log('   📦 Returned object type:', typeof videoProcessor);
            console.log('   🔧 Available methods:', Object.getOwnPropertyNames(videoProcessor.__proto__));

            // Wait for callbacks with timeout
            console.log('\n2. Waiting for callbacks (30 second timeout):');
            console.log('   ⏳ Monitoring callback activity...');

            const timeoutPromise = new Promise((resolve) => {
                setTimeout(() => {
                    resolve('timeout');
                }, 30000);
            });

            const callbackPromise = new Promise((resolve) => {
                const checkCallbacks = () => {
                    if (callbackStats.onEventCalled && callbackStats.frameCount >= 3) {
                        resolve('success');
                    } else {
                        setTimeout(checkCallbacks, 100);
                    }
                };
                checkCallbacks();
            });

            const result = await Promise.race([timeoutPromise, callbackPromise]);

            console.log('\n3. Callback verification results:');
            console.log(`   📡 onEvent called: ${callbackStats.onEventCalled ? '✅ YES' : '❌ NO'}`);
            console.log(`   🛑 onStop called: ${callbackStats.onStopCalled ? '✅ YES' : '❌ NO'}`);
            console.log(`   ❌ onError called: ${callbackStats.onErrorCalled ? '✅ YES' : '❌ NO'}`);
            console.log(`   🎬 onFrame called: ${callbackStats.onFrameCalled ? '✅ YES' : '❌ NO'}`);
            console.log(`   📊 Total frames received: ${callbackStats.frameCount}`);

            if (result === 'timeout') {
                console.log('\n   ⚠️  TIMEOUT: No sufficient callback activity detected within 30 seconds');
                console.log('   🔍 This indicates a potential issue with the async callback mechanism');
            } else {
                console.log('\n   🎉 SUCCESS: Configuration object callbacks are working correctly!');
            }

            // Test stop with reason
            console.log('\n4. Testing stop with reason:');
            if (videoProcessor && typeof videoProcessor.stop === 'function') {
                // Test stop with custom reason
                setTimeout(() => {
                    console.log('   🧪 Testing stop with reason 42...');
                    videoProcessor.stop(42);
                }, 1000);
                
                // Wait a bit for stop callback
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            // Clean shutdown
            console.log('\n5. Final cleanup:');
            if (videoProcessor && typeof videoProcessor.stop === 'function') {
                const stopResult = videoProcessor.stop();
                console.log(`   🛑 VideoProcessor stopped: ${stopResult ? '✅ SUCCESS' : '⚠️  FAILED'}`);
            }

        } else {
            console.log('   ❌ Failed to create VideoProcessor instance');
            console.log('   🔍 Check if the addon was built correctly and WAV file exists');
        }

    } catch (error) {
        console.error('❌ Test failed with error:', error);
        console.error('Stack trace:', error.stack);
    }
}

// Test error handling with invalid configuration
async function runErrorHandlingTest() {
    console.log('\n🧪 Testing error handling with invalid configurations:');
    
    try {
        console.log('   1. Testing with no arguments...');
        addon.createIQVideoProcessor();
        console.log('   ❌ Should have thrown an error');
    } catch (error) {
        console.log('   ✅ Correctly threw error:', error.message);
    }
    
    try {
        console.log('   2. Testing with non-object argument...');
        addon.createIQVideoProcessor("invalid");
        console.log('   ❌ Should have thrown an error');
    } catch (error) {
        console.log('   ✅ Correctly threw error:', error.message);
    }
    
    try {
        console.log('   3. Testing with incomplete configuration object...');
        addon.createIQVideoProcessor({
            onEvent: () => {},
            // Missing other required callbacks
        });
        console.log('   ❌ Should have thrown an error or failed gracefully');
    } catch (error) {
        console.log('   ✅ Correctly handled incomplete config:', error.message);
    }
}

// Run all tests
async function runAllTests() {
    console.log('🚀 Starting comprehensive configuration object tests...\n');
    
    await runConfigObjectTest();
    await runErrorHandlingTest();
    
    console.log('\n✅ All tests completed!');
}

// Execute tests
runAllTests().catch(console.error);
