#include <iostream>
#include <thread>
#include <chrono>
#include <atomic>
#include <vector>
#include <cassert>
#include "../pipeline/output-queue-node/output_queue_node.h"
#include "../../stream-pipeline/passthrough_link.h"
#include "../types.h"

namespace OutputQueueNodeTests {

// Test helper class to access private process method
class TestableOutputQueueNode : public IQVideoProcessor::Pipeline::OutputQueueNode {
public:
    TestableOutputQueueNode(size_t maxQueueSize, std::function<bool()> controlLambda)
        : OutputQueueNode(maxQueueSize, std::move(controlLambda)) {}

    // Expose the private process method for testing
    bool testProcess(IQVideoProcessor::Pipeline::FrameCompositionResult& result) {
        return process(result);
    }
};

// Helper function to create test FrameCompositionResult
IQVideoProcessor::Pipeline::FrameCompositionResult createTestResult(size_t frameNumber, size_t dataSize = 1024) {
    IQVideoProcessor::Pipeline::FrameCompositionResult result;
    result.frameNumber = frameNumber;
    result.dataSize = dataSize;
    result.width = 640;
    result.height = 480;
    result.videoStandard = IQVideoProcessor::VideoStandard::NTSC_STANDARD;
    result.data.resize(dataSize);
    
    // Fill with test pattern
    for (size_t i = 0; i < dataSize; ++i) {
        result.data[i] = static_cast<uint8_t>(frameNumber + i);
    }
    
    return result;
}

void test_construction() {
    std::cout << "Testing OutputQueueNode construction..." << std::endl;

    // Test with different queue sizes
    std::vector<size_t> testSizes = {1, 10, 100, 1000};

    for (auto size : testSizes) {
        auto node = std::make_unique<TestableOutputQueueNode>(
            size, []() { return true; });

        // Should start with empty queue
        assert(!node->hasResults());
        std::cout << "✓ Queue size " << size << " constructed successfully" << std::endl;
    }
}

void test_basic_queue_operations() {
    std::cout << "Testing basic queue operations..." << std::endl;

    auto node = std::make_unique<TestableOutputQueueNode>(
        5, []() { return true; });

    // Initially empty
    assert(!node->hasResults());

    // Add some results via process method
    for (size_t i = 0; i < 3; ++i) {
        auto result = createTestResult(i);
        node->testProcess(result);
    }

    // Should have results now
    assert(node->hasResults());

    // Retrieve results in FIFO order
    for (size_t i = 0; i < 3; ++i) {
        assert(node->hasResults());
        auto& result = node->getNextResult();
        assert(result.frameNumber == i);
        std::cout << "✓ Retrieved frame " << i << " correctly" << std::endl;
    }

    // Should be empty again
    assert(!node->hasResults());

    std::cout << "✓ Basic queue operations work correctly" << std::endl;
}

void test_queue_overflow() {
    std::cout << "Testing queue overflow handling..." << std::endl;

    const size_t maxSize = 3;
    auto node = std::make_unique<TestableOutputQueueNode>(
        maxSize, []() { return true; });

    // Fill queue to capacity
    for (size_t i = 0; i < maxSize; ++i) {
        auto result = createTestResult(i);
        node->testProcess(result);
    }

    // Add one more - should drop oldest
    auto result = createTestResult(maxSize);
    node->testProcess(result);

    // Should still have maxSize results
    assert(node->hasResults());

    // First result should be frame 1 (frame 0 was dropped)
    auto& firstResult = node->getNextResult();
    assert(firstResult.frameNumber == 1);

    std::cout << "✓ Queue overflow handled correctly (oldest dropped)" << std::endl;
}

void test_empty_queue_exception() {
    std::cout << "Testing empty queue exception..." << std::endl;

    auto node = std::make_unique<TestableOutputQueueNode>(
        5, []() { return true; });

    // Should throw when getting from empty queue
    bool exceptionThrown = false;
    try {
        node->getNextResult();
    } catch (const std::runtime_error& e) {
        exceptionThrown = true;
        std::cout << "✓ Expected exception thrown: " << e.what() << std::endl;
    }

    assert(exceptionThrown);
    std::cout << "✓ Empty queue exception handling works correctly" << std::endl;
}

void test_thread_safety() {
    std::cout << "Testing thread safety..." << std::endl;
    
    const size_t maxSize = 100;
    const size_t numProducers = 3;
    const size_t itemsPerProducer = 50;
    
    auto node = std::make_unique<TestableOutputQueueNode>(
        maxSize, []() { return true; });

    std::atomic<size_t> totalProduced{0};
    std::atomic<size_t> totalConsumed{0};
    std::atomic<bool> stopConsumer{false};

    // Producer threads
    std::vector<std::thread> producers;
    for (size_t p = 0; p < numProducers; ++p) {
        producers.emplace_back([&, p]() {
            for (size_t i = 0; i < itemsPerProducer; ++i) {
                auto result = createTestResult(p * itemsPerProducer + i);
                node->testProcess(result);
                totalProduced++;
                std::this_thread::sleep_for(std::chrono::microseconds(10));
            }
        });
    }
    
    // Consumer thread
    std::thread consumer([&]() {
        while (!stopConsumer || node->hasResults()) {
            if (node->hasResults()) {
                try {
                    auto& result = node->getNextResult();
                    totalConsumed++;
                    std::this_thread::sleep_for(std::chrono::microseconds(5));
                } catch (const std::runtime_error&) {
                    // Queue became empty between check and retrieval - that's ok
                }
            } else {
                std::this_thread::sleep_for(std::chrono::microseconds(1));
            }
        }
    });
    
    // Wait for producers to finish
    for (auto& producer : producers) {
        producer.join();
    }
    
    // Signal consumer to stop and wait
    stopConsumer = true;
    consumer.join();
    
    std::cout << "✓ Produced: " << totalProduced << ", Consumed: " << totalConsumed << std::endl;
    std::cout << "✓ Thread safety test completed successfully" << std::endl;
}

void test_memory_efficiency() {
    std::cout << "Testing memory efficiency (copy operations)..." << std::endl;

    auto node = std::make_unique<TestableOutputQueueNode>(
        10, []() { return true; });

    // Create a large test result to make copies more noticeable
    const size_t largeDataSize = 1024 * 1024; // 1MB
    auto result = createTestResult(42, largeDataSize);

    // Process (first copy)
    node->testProcess(result);

    // Retrieve (second copy)
    auto& retrievedResult = node->getNextResult();

    // Verify data integrity
    assert(retrievedResult.frameNumber == 42);
    assert(retrievedResult.dataSize == largeDataSize);
    assert(retrievedResult.data.size() == largeDataSize);

    // Verify data content
    for (size_t i = 0; i < 100; ++i) { // Check first 100 bytes
        assert(retrievedResult.data[i] == static_cast<uint8_t>(42 + i));
    }

    std::cout << "✓ Memory efficiency test passed (2 copies confirmed)" << std::endl;
}

void test_control_lambda_functionality() {
    std::cout << "Testing control lambda functionality..." << std::endl;

    std::atomic<bool> shouldContinue{true};
    auto node = std::make_unique<TestableOutputQueueNode>(
        5, [&shouldContinue]() { return shouldContinue.load(); });

    // Process should work when lambda returns true
    auto result1 = createTestResult(1);
    bool processResult1 = node->testProcess(result1);
    assert(processResult1 == true);

    // Change lambda to return false
    shouldContinue = false;

    // Process should stop when lambda returns false
    auto result2 = createTestResult(2);
    bool processResult2 = node->testProcess(result2);
    assert(processResult2 == false);

    std::cout << "✓ Control lambda functionality works correctly" << std::endl;
}

} // namespace OutputQueueNodeTests

// Main test runner function
int run_output_queue_node_tests() {
    std::cout << "\n🧪 Running OutputQueueNode Tests" << std::endl;
    std::cout << "=================================" << std::endl;

    try {
        OutputQueueNodeTests::test_construction();
        OutputQueueNodeTests::test_basic_queue_operations();
        OutputQueueNodeTests::test_queue_overflow();
        OutputQueueNodeTests::test_empty_queue_exception();
        OutputQueueNodeTests::test_thread_safety();
        OutputQueueNodeTests::test_memory_efficiency();
        OutputQueueNodeTests::test_control_lambda_functionality();

        std::cout << "\n🎉 All OutputQueueNode tests PASSED!" << std::endl;
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "\n❌ OutputQueueNode test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "\n❌ OutputQueueNode test failed with unknown exception" << std::endl;
        return 1;
    }
}

// Main function for standalone execution
int main() {
    return run_output_queue_node_tests();
}
