#include "./frame_composition_node.h"
#include <iostream>
#include <sstream>

namespace IQVideoProcessor::Pipeline {

FrameCompositionNode::FrameCompositionNode(const SampleRateType sampleRate): sampleRate_(sampleRate) {
  setRunning();
}

FrameCompositionNode::~FrameCompositionNode() {
  PipelineComponent::stop();
}

bool FrameCompositionNode::process(LineDetectionEvent& event) {
  if (!running()) return false;

  // Route events to appropriate handlers based on event type
  switch (event.type) {
    case LineDetectionEventType::STANDARD_DETECTED:
      handleStandardDetected(event.videoStandard);
      break;
    case LineDetectionEventType::SYNC_LOCKED:
      handleSyncLock();
      break;
    case LineDetectionEventType::SYNC_LOCK_LOST:
      handleSyncLockLost();
      break;
    case LineDetectionEventType::FRAME_BEGIN:
      handleFrameBegin(event.frameNumber, event.odd);
      break;
    case LineDetectionEventType::FRAME_END:
      handleFrameEnd(event.frameNumber, event.odd);
      break;
    case LineDetectionEventType::LINE_RECEIVED:
      handleLineReceived(event.data, event.dataSize, event.lineNumber, event.odd);
      break;
    case LineDetectionEventType::EQUALIZATION:
      handleEqualization(event.data, event.dataSize, event.odd);
      break;
    case LineDetectionEventType::UNKNOWN:
    default:
      handleUnknownEvent();
      break;
  }

  // Continue passing events through the pipeline
  // return sendOutput(event);
  return running();
}

void FrameCompositionNode::handleStandardDetected(const VideoStandard standard) {
  if (standard == UNKNOWN_VIDEO_STANDARD) {
    isStandardDetected_ = false;
    currentStandard_ = UNKNOWN_VIDEO_STANDARD;
    frameCanvas_.reset();
    std::cout << "FrameCompositionNode: Warning - Unknown video standard detected" << std::endl;
    return;
  }

  if (currentStandard_ == standard) return; // No change
  std::cout << "FrameCompositionNode: Standard changed from " << currentStandard_ << " to " << standard << std::endl;

  // Prepare the frame canvas for the new standard, allocating necessary resources
  auto [width, height] = getVideoStandardDimensions(standard);
  frameCanvas_.reset();
  frameCanvas_ = std::make_unique<FrameCanvas>(width, height);
  currentStandard_ = standard;
  isInterlaced_ = isVideoStandardInterlaced(standard);
  auto [leftPaddingSec, rightPaddingSec] = getVideoStandardPaddings(standard);
  lineLeftPadding_ = static_cast<size_t>(std::ceil(leftPaddingSec * sampleRate_));
  lineRightPadding_ = static_cast<size_t>(std::ceil(rightPaddingSec * sampleRate_));
  lineHorizontalPadding_ = lineLeftPadding_ + lineRightPadding_;

  // Prepare the composed frame structure, reserving space for rendered data
  currentCompositionResult_.data.resize(frameCanvas_->getMinRenderBufferSize());
  currentCompositionResult_.width = width;
  currentCompositionResult_.height = height;
  currentCompositionResult_.videoStandard = standard;
  currentCompositionResult_.frameNumber = 0;
  currentCompositionResult_.dataSize = 0;

  equalizationBlackLevel_ = 0.0f; // TODO Reset to default or calculated value
  isStandardDetected_ = true;
}

void FrameCompositionNode::handleSyncLock() {
  std::cout << "FrameCompositionNode: Sync lock acquired" << std::endl;
  isSyncLocked_ = true;
}

void FrameCompositionNode::handleSyncLockLost() {
  std::cout << "FrameCompositionNode: Sync lock lost" << std::endl;
  isSyncLocked_ = false;
  // TODO Handle the already received incomplete frame if needed
}

void FrameCompositionNode::handleFrameBegin(const size_t frameNumber, const bool isOdd) {
  if (!isStandardDetected_ || !isSyncLocked_) {
    std::cout << "FrameCompositionNode: Warning - Frame begin received but standard not detected or sync not locked" << std::endl;
    return;
  }

  if (!currentProcessingFrame_.has_value()) {
    if (!isOdd) return; // We expect the first part to be odd in any case, so ignore even part if no frame is active
    currentProcessingFrame_ = FrameInfo{frameNumber, true, false};
  } else {
    if (isOdd || !isInterlaced_) {
      // We can't have the second odd part or normally reach here in non-interlaced mode
      processCompleteFrame(); // So we end the previous frame first, and begin a new one starting from odd part
      currentProcessingFrame_ = FrameInfo{frameNumber, true, false};
      std::cout << "FrameCompositionNode: Frame " << frameNumber << " begin (ODD field, new frame)" << std::endl;
      return;
    }
    // We are receiving the even part of the current frame
    auto &cfi = currentProcessingFrame_.value();
    cfi.hasEvenPart = true;
    // std::cout << "FrameCompositionNode: Frame " << frameNumber << " begin (EVEN field)" << std::endl;
  }
}

void FrameCompositionNode::handleFrameEnd(const size_t frameNumber, const bool isOdd) {
  // We don't care about the detected standard or sync state here, just validate frame info
  if (!currentProcessingFrame_.has_value()) return;

  const auto &cfi = currentProcessingFrame_.value(); // Copy current frame info
  if ((cfi.hasOddPart && cfi.hasEvenPart) || !isInterlaced_) {
    processCompleteFrame(); // Frame is complete, lets process it
  }
}

void FrameCompositionNode::handleLineReceived(
  const std::vector<TFloat> &data,
  const size_t dataSize,
  const size_t lineNumber,
  const bool isOdd
) {
  // Sanity checks
  if (!isStandardDetected_) {
    std::cout << "FrameCompositionNode: Warning - Line received but standard not detected" << std::endl;
    return;
  }
  if (!currentProcessingFrame_.has_value()) {
    std::cout << "FrameCompositionNode: Warning - Line received but no frame is active" << std::endl;
    return;
  }

  const auto croppedDataStart = lineLeftPadding_;
  const auto croppedDataSize = dataSize > lineHorizontalPadding_ ? dataSize - lineHorizontalPadding_ : 0;
  if (croppedDataSize == 0) {
    std::cout << "FrameCompositionNode: Warning - Line received but padding exceeds data size" << std::endl;
    return;
  }

  if (isInterlaced_) {
    const auto lineIndex = isOdd ? (lineNumber * 2 + 1) : (lineNumber * 2);
    frameCanvas_->setVideoLineRawData(lineIndex, &data[croppedDataStart], croppedDataSize);
  } else {
    frameCanvas_->setVideoLineRawData(lineNumber, &data[croppedDataStart], croppedDataSize);
  }

  // std::cout << "FrameCompositionNode: Line " << lineNumber << " received ("<< (isOdd ? "ODD" : "EVEN") << " field), " << dataSize << " samples" << std::endl;
}

void FrameCompositionNode::handleEqualization(const std::vector<TFloat>& data, const size_t dataSize, const bool isOdd) {
  // std::cout << "FrameCompositionNode: Equalization pulse received (" << (isOdd ? "ODD" : "EVEN") << " field), " << dataSize << " samples" << std::endl;
  // TODO Calculate the average level and accumulate over multiple equalization pulses for better accuracy
  equalizationBlackLevel_ = data[dataSize >> 1]; // Take the middle sample as blanking level estimate
}

void FrameCompositionNode::handleUnknownEvent() {
  std::cout << "FrameCompositionNode: Unknown event received" << std::endl;
}

void FrameCompositionNode::processCompleteFrame() {
  if (!currentProcessingFrame_.has_value()) return; // No frame to process
  const auto &cfi = currentProcessingFrame_.value(); // Copy current frame info
  // std::cout << "FrameCompositionNode: Ending frame " << cfi.frameNumber << " (hasOddPart: " << cfi.hasOddPart << ", hasEvenPart: " << cfi.hasEvenPart << ")" << std::endl;

  const auto renderResult = frameCanvas_->render(
    &currentCompositionResult_.data[0],
    currentCompositionResult_.data.size(),
    equalizationBlackLevel_
  );

  if (renderResult.has_value()) {
    currentCompositionResult_.dataSize = renderResult.value();
    currentCompositionResult_.frameNumber = cfi.frameNumber;

    if (!sendOutput(currentCompositionResult_)) {
      stop();
    }
  } else {
    std::cout << "FrameCompositionNode: Warning - Failed to render frame " << cfi.frameNumber << std::endl;
  }

  currentProcessingFrame_.reset(); // Clear current frame info
}

} // namespace IQVideoProcessor::Pipeline
