#include "./output_queue_node.h"
#include <iostream>
#include <sstream>
#include <fstream>

namespace IQVideoProcessor::Pipeline {

OutputQueueNode::OutputQueueNode(const size_t maxQueueSize, std::function<bool()> onFrameReceived)
  : onFrameReceived_(std::move(onFrameReceived)),
    maxQueueSize_(maxQueueSize) {
  setRunning();
}

OutputQueueNode::~OutputQueueNode() {
  PipelineComponent::stop();
}

bool OutputQueueNode::process(FrameCompositionResult& result) {
  if (!running()) return false;

  writeCompositionResultToFile(result, fileCounter_++);

  {
    std::lock_guard lock(queueMutex_);
    // Handle queue overflow - drop the oldest result if at capacity
    if (queue_.size() >= maxQueueSize_) {
      queue_.pop(); // Remove oldest result
    }
    queue_.push(result); // Copy result into queue
  }

  if (!onFrameReceived_()) {
    stop();
    return false;
  }

  return running();
}

bool OutputQueueNode::hasNextFrame() const {
  std::lock_guard lock(queueMutex_);
  return !queue_.empty();
}

FrameCompositionResult& OutputQueueNode::getNextFrame() {
  std::lock_guard lock(queueMutex_);
  lastRetrievedResult_ = std::move(queue_.front());
  queue_.pop();
  return lastRetrievedResult_;
}

void OutputQueueNode::writeCompositionResultToFile(const FrameCompositionResult& result, const size_t fileNumber) {
  std::ostringstream filename;
  filename << "frames/frame_" << fileNumber << ".jpg";

  std::ofstream file(filename.str(), std::ios::binary);
  if (!file.is_open()) {
    std::cerr << "OutputQueueNode: Failed to open output file: " << filename.str() << std::endl;
    return;
  }

  file.write(reinterpret_cast<const char*>(&result.data[0]), result.dataSize);
  const bool writeSuccess = file.good();
  file.close();

  if (!writeSuccess) {
    std::cerr << "OutputQueueNode: Failed to write JPEG data to file: " << filename.str() << std::endl;
  } else {
    std::cout << "OutputQueueNode: Successfully wrote frame " << result.frameNumber << " to " << filename.str() << " (" << result.dataSize << " bytes)" << std::endl;
  }
}

} // namespace IQVideoProcessor::Pipeline
