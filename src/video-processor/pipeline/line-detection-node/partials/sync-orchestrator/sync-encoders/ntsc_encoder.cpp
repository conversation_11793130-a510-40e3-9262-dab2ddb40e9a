#include "./ntsc-encoder.h"

namespace IQVideoProcessor::Pipeline::SyncEncoders {

NTSCSyncEncoder::NTSCSyncEncoder(const VideoSyncEncoder::Config& config): VideoSyncEncoder(config) {
  lineDistance_ = config.sampleRate / 15734.0; // ~63.5 µs
  halfLineDistance_ = lineDistance_ * 0.5;       // ~31.75 µs

  // Unstandard distances, appeared due to processing using center positions of impulse instead of falling fronts
  lastVerticalToEqualizingDistance_ = lineDistance_ * 0.30703;

  lastEqualizingToOddHorizontalDistance_ = lineDistance_ * 0.5184353; // a little bit longer than H/2 (as we are processing using center position of impulse)
  lastEqualizingToEvenHorizontalDistance_ = lineDistance_ * 1.017315486; // a little bit longer than H (as we are processing using center position of impulse)
  lastEqualizingToVerticalDistance_ = lineDistance_ * 0.6941391257;

  lastEvenHorizontalToEqualingDistance_ = lineDistance_ * 0.9809474686; // a little bit shorter than H (as we are processing using center position of impulse)
  lastOddHorizontalToEqualingDistance_ = lineDistance_ * 0.4805613143; // a little bit shorter than H/2 (as we are processing using center position of impulse)

  lineDistanceTolerance_ = lineDistance_ * 0.015; // +-1.5%;
  halfLineDistanceTolerance_ = lineDistanceTolerance_; // +-1.5%; for now

  // ODD
  fillOddOrEvenVerticalEncoders(oddVerticalStartIdx_, oddVerticalLastIdx_, true); // First 6 vertical sync pulses in the odd field
  fillOddPostEqualizingEncoders(oddPostEqualizingStartIdx_); // Then 6 equalizing pulses in the odd field
  fillOddOrEvenHorizontalEncoders(oddHorizontalStartIdx_, true); // Then 253 horizontal pulses in the odd field
  fillOddOrEvenPreEqualizingEncoders(oddPreEqualizingStartIdx_, oddPreEqualizingLastIdx_, true); // Then 6 equalizing pulses in the odd field
  // EVEN
  fillOddOrEvenVerticalEncoders(evenVerticalStartIdx_, evenVerticalLastIdx_, false); // First 6 vertical sync pulses in the even field
  fillEvenPostEqualizingEncoders(evenPostEqualizingStartIdx_); // Then 6 equalizing pulses in the even field
  fillOddOrEvenHorizontalEncoders(evenHorizontalStartIdx_, false); // Then 253 horizontal pulses in the even field
  fillOddOrEvenPreEqualizingEncoders(evenPreEqualizingStartIdx_, evenPreEqualizingLastIdx_, false);
}

void NTSCSyncEncoder::transit(const Transition transition) {
  switch (transition) {
    case Transition::NEXT: return transitNext();
    case Transition::TO_ODD_PRE_EQUALIZING_LAST: return setEncoderIdx(oddPreEqualizingLastIdx_);
    case Transition::TO_ODD_VERTICAL_START: return setEncoderIdx(oddVerticalStartIdx_);
    case Transition::TO_ODD_VERTICAL_LAST: return setEncoderIdx(oddVerticalLastIdx_);
    case Transition::TO_ODD_HORIZONTAL_START: return setEncoderIdx(oddHorizontalStartIdx_);
    case Transition::TO_EVEN_HORIZONTAL_START: return setEncoderIdx(evenHorizontalStartIdx_);
    default: break;
  }
}

void NTSCSyncEncoder::fillOddOrEvenVerticalEncoders(size_t &startIdx, size_t &lastIdx, bool isOdd) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  auto distance = halfLineDistance_;
  const auto tolerance = halfLineDistanceTolerance_;

  // The first 5 expect to be followed only by another vertical sync pulse
  const Range<double> nextVerticalSyncExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { VERTICAL_SYNC_PULSE, nextVerticalSyncExpectedDistanceRange, distance, Transition::NEXT },
  };
  for (auto i = 0; i < 5; ++i) {
    encoders.emplace_back(VERTICAL_SYNC_PULSE, false, isOdd, possibleFutures, FrameRegion::VERTICAL_SYNC, 0);
  }

  lastIdx = encoders.size();

  // The last one expects to be followed by an equalizing pulse
  distance = lastVerticalToEqualizingDistance_;
  const Range<double> nextEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> lastPossibleFutures = {
    { EQUALIZING_SYNC_PULSE, nextEqualizingExpectedDistanceRange, distance, Transition::NEXT },
  };
  encoders.emplace_back(VERTICAL_SYNC_PULSE, false, isOdd, lastPossibleFutures, FrameRegion::VERTICAL_SYNC, 0);
}

void NTSCSyncEncoder::fillOddPostEqualizingEncoders(size_t &startIdx) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  const auto distance = halfLineDistance_;
  const auto tolerance = halfLineDistanceTolerance_;
  // The first 5 expect to be followed with half line distance equalizing pulse
  const Range<double> nextEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { EQUALIZING_SYNC_PULSE, nextEqualizingExpectedDistanceRange, distance, Transition::NEXT },
  };
  for (auto i = 0; i < 5; ++i) {
    encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, true, possibleFutures, FrameRegion::POST_EQUALIZING, 0);
  }
  // The 6th, depend on odd/even field, expects to be followed by half or full line distance horizontal pulse
  const auto toOddDistance =  lastEqualizingToOddHorizontalDistance_;
  const auto toEvenDistance = lastEqualizingToEvenHorizontalDistance_;

  const Range<double> nextOddHorizontalExpectedDistanceRange{toOddDistance - tolerance, toOddDistance + tolerance};
  const Range<double> nextEvenHorizontalExpectedDistanceRange{toEvenDistance - tolerance, toEvenDistance + tolerance};
  const std::vector<EIFuture> lastPossibleFutures = {
    { HORIZONTAL_SYNC_PULSE, nextOddHorizontalExpectedDistanceRange, toOddDistance, Transition::NEXT }, // The next 6th pulse is equalizing, and we are still in the odd frame, continue
    { HORIZONTAL_SYNC_PULSE, nextEvenHorizontalExpectedDistanceRange, toEvenDistance, Transition::TO_EVEN_HORIZONTAL_START }, // We are switching to even frame phase
  };
  encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, true, lastPossibleFutures, FrameRegion::POST_EQUALIZING, 0);
}

void NTSCSyncEncoder::fillOddOrEvenHorizontalEncoders(size_t &startIdx, bool isOdd) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  auto distance = lineDistance_;
  const auto tolerance = lineDistanceTolerance_;
  // The first 13 expect to be followed with line distance horizontal pulse, and doesn't cary video information
  const Range<double> nextHorizontalExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { HORIZONTAL_SYNC_PULSE, nextHorizontalExpectedDistanceRange, distance, Transition::NEXT },
  };
  for (auto i = 0; i < 13; ++i) {
    encoders.emplace_back(HORIZONTAL_SYNC_PULSE, false, isOdd, possibleFutures, FrameRegion::HORIZONTAL_LINES, 0);
  }

  const auto impulses = isOdd ? 240 : 239;
  // The next 240->odd / 239->even expect to be followed with line distance horizontal pulse, and cary video information
  for (auto i = 0; i < impulses; ++i) {
    encoders.emplace_back(HORIZONTAL_SYNC_PULSE, true, isOdd, possibleFutures, FrameRegion::HORIZONTAL_LINES, i);
  }

  if (isOdd) {
    // The last 241st is invisible, half-line distance horizontal region, followed be equalizing pulse
    distance = lastOddHorizontalToEqualingDistance_; // half line distance to the next equalizing pulse
    const auto hlTolerance = halfLineDistanceTolerance_;
    const Range<double> nextPreEqualizingExpectedDistanceRange{distance - hlTolerance, distance + hlTolerance};
    const std::vector<EIFuture> lastPossibleFutures = {
      { EQUALIZING_SYNC_PULSE, nextPreEqualizingExpectedDistanceRange, distance, Transition::NEXT },
    };
    encoders.emplace_back(HORIZONTAL_SYNC_PULSE, false, isOdd, lastPossibleFutures, FrameRegion::HORIZONTAL_LINES, 0);
  } else {
    // The last 240th expect to be followed with line distance pre equalizing pulse, and cary video information
    distance = lastEvenHorizontalToEqualingDistance_;
    const Range<double> nextPreEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
    const std::vector<EIFuture> lastPossibleFutures = {
      { EQUALIZING_SYNC_PULSE, nextPreEqualizingExpectedDistanceRange, distance, Transition::NEXT },
    };
    encoders.emplace_back(HORIZONTAL_SYNC_PULSE, true, isOdd, lastPossibleFutures, FrameRegion::HORIZONTAL_LINES, impulses);
  }
}

void NTSCSyncEncoder::fillOddOrEvenPreEqualizingEncoders(size_t &startIdx, size_t &lastIdx, bool isOdd) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  const auto distance = halfLineDistance_;
  const auto tolerance = halfLineDistanceTolerance_;
  // The first 5 expect to be followed with half line distance equalizing pulse
  const Range<double> nextEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { EQUALIZING_SYNC_PULSE, nextEqualizingExpectedDistanceRange, distance, Transition::NEXT },
  };
  for (auto i = 0; i < 5; ++i) {
    encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, isOdd, possibleFutures, FrameRegion::PRE_EQUALIZING, 0);
  }

  lastIdx = encoders.size();

  const auto toVerticalDistance = lastEqualizingToVerticalDistance_;
  const Range<double> lastVerticalExpectedDistanceRange{toVerticalDistance - tolerance, toVerticalDistance + tolerance};
  const std::vector<EIFuture> lastPossibleFutures = {
    { VERTICAL_SYNC_PULSE, lastVerticalExpectedDistanceRange, toVerticalDistance, Transition::NEXT }, // We are switching to even phase
  };
  encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, true, lastPossibleFutures, FrameRegion::PRE_EQUALIZING, 0);
}

void NTSCSyncEncoder::fillEvenPostEqualizingEncoders(size_t &startIdx) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  const auto distance = halfLineDistance_;
  const auto tolerance = halfLineDistanceTolerance_;
  // The first 5 expect to be followed with half line distance equalizing pulse
  const Range<double> nextEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { EQUALIZING_SYNC_PULSE, nextEqualizingExpectedDistanceRange, distance, Transition::NEXT },
  };
  for (auto i = 0; i < 5; ++i) {
    encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, false, possibleFutures, FrameRegion::POST_EQUALIZING, 0);
  }
  // The 6th, depend on odd/even field, expects to be followed by half or full line distance horizontal pulse
  const auto toEvenDistance = lastEqualizingToEvenHorizontalDistance_;
  const auto toOddDistance =  lastEqualizingToOddHorizontalDistance_;

  const Range<double> nextEvenHorizontalExpectedDistanceRange{toEvenDistance - tolerance, toEvenDistance + tolerance};
  const Range<double> nextOddHorizontalExpectedDistanceRange{toOddDistance - tolerance, toOddDistance + tolerance};
  const std::vector<EIFuture> lastPossibleFutures = {
    { HORIZONTAL_SYNC_PULSE, nextEvenHorizontalExpectedDistanceRange, toEvenDistance, Transition::NEXT },
    { HORIZONTAL_SYNC_PULSE, nextOddHorizontalExpectedDistanceRange, toOddDistance, Transition::TO_ODD_HORIZONTAL_START },
  };
  encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, false, lastPossibleFutures, FrameRegion::POST_EQUALIZING, 0);
}

} // namespace IQVideoProcessor::Pipeline::SyncEncoders
