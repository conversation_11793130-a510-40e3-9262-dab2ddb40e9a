#include "./video-sync-encoder.h"
#include <cassert>

namespace IQVideoProcessor::Pipeline::SyncEncoders {

// Static member definition
int32_t EncoderItem::globalItemId_ = 0;

EncoderItem::EncoderItem(
  const VideoSyncPulseType pt,
  const bool isVisible,
  const bool isOdd,
  std::vector<EIFuture> f,
  const FrameRegion frameRegion,
  const size_t visLineIdx,
  const size_t defaultIdx
) {
  assert(!f.empty());
  id = nextItemId();
  pulseType = pt;
  futures = std::move(f);
  defaultFutureIdx = defaultIdx;
  maxFutureDistance = 0;
  visible = isVisible;
  odd = isOdd;
  region = frameRegion;
  visibleLineIndex = visLineIdx;
  for (const auto& ef : futures) {
    if (ef.inRange.to > maxFutureDistance) {
      maxFutureDistance = ef.inRange.to;
    }
  }
}

inline bool EncoderItem::canEstimateFutureByDistance(const double distance) const {
  return distance >= maxFutureDistance;
}

inline int32_t EncoderItem::nextItemId() {
  return globalItemId_++; 
}

inline void EncoderItem::resetItemId() {
  globalItemId_ = 0; 
}

VideoSyncEncoder::VideoSyncEncoder(const Config & config) : config_(config) {}

const EncoderItem& VideoSyncEncoder::currentItem() const {
  return getEncoders()[getEncoderIdx()]; 
}

const EncoderItem& VideoSyncEncoder::nextItem() const {
  return getEncoders()[getNextEncoderIdx()]; 
}

void VideoSyncEncoder::reset() {
  setEncoderIdx(0); 
}

inline size_t VideoSyncEncoder::getEncoderIdx() const {
  return encoderIdx_; 
}

inline size_t VideoSyncEncoder::getNextEncoderIdx() const {
  const auto idx = encoderIdx_ + 1;
  return idx >= encoders_.size() ? 0 : idx;
}

void VideoSyncEncoder::setEncoderIdx(const size_t idx) {
  encoderIdx_ = idx; 
}

const std::vector<EncoderItem>& VideoSyncEncoder::getEncoders() const {
  return encoders_; 
}

std::vector<EncoderItem>& VideoSyncEncoder::getWritableEncoders() {
  return encoders_;
}

void VideoSyncEncoder::transitNext() {
  setEncoderIdx(getNextEncoderIdx()); 
}

} // namespace IQVideoProcessor::Pipeline::SyncEncoders
