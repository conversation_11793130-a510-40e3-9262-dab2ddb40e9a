#pragma once
#include "./video-sync-encoder.h"

// NTSC standard: https://en.wikipedia.org/wiki/NTSC
// Total lines: 525
// Visible lines: 486
// Frame rate: 29.97 fps
// Horizontal frequency: ~15.734 kHz
// Vertical frequency: ~59.94 Hz (interlaced, so 29.97 frames per second)
// Line duration: ~63.5 µs (1 / 15,734 Hz)
// Horizontal sync pulse: ~4.7 µs
// Equalizing pulses: ~2.3 µs
// Vertical sync pulse: ~27.3 µs

namespace IQVideoProcessor::Pipeline::SyncEncoders {

class NTSCSyncEncoder final : public VideoSyncEncoder {
public:
  explicit NTSCSyncEncoder(const VideoSyncEncoder::Config& config);
  ~NTSCSyncEncoder() override = default;

  void transit(Transition transition) override;

private:
  void fillOddOrEvenVerticalEncoders(size_t &startIdx, size_t &lastIdx, bool isOdd);
  void fillOddPostEqualizingEncoders(size_t &startIdx);
  void fillOddOrEvenHorizontalEncoders(size_t &startIdx, bool isOdd);
  void fillOddOrEvenPreEqualizingEncoders(size_t &startIdx, size_t &lastIdx, bool isOdd);
  void fillEvenPostEqualizingEncoders(size_t &startIdx);

  // Distances in samples
  double lineDistance_;
  double halfLineDistance_;
  // Exotic distances in samples (appear due to processing using center positions of impulse instead of falling fronts)
  double lastVerticalToEqualizingDistance_;
  double lastEqualizingToOddHorizontalDistance_;
  double lastEqualizingToEvenHorizontalDistance_;
  double lastEqualizingToVerticalDistance_;
  double lastEvenHorizontalToEqualingDistance_;
  double lastOddHorizontalToEqualingDistance_;

  // Tolerance
  double lineDistanceTolerance_;
  double halfLineDistanceTolerance_;

  // Encoder indices for transitions
  size_t oddVerticalStartIdx_{0};
  size_t oddVerticalLastIdx_{0};
  size_t oddPostEqualizingStartIdx_{0};
  size_t oddHorizontalStartIdx_{0};
  size_t oddPreEqualizingStartIdx_{0};
  size_t oddPreEqualizingLastIdx_{0};
  size_t evenVerticalStartIdx_{0};
  size_t evenVerticalLastIdx_{0};
  size_t evenPostEqualizingStartIdx_{0};
  size_t evenHorizontalStartIdx_{0};
  size_t evenPreEqualizingStartIdx_{0};
  size_t evenPreEqualizingLastIdx_{0};
};

}