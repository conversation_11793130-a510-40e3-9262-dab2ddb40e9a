#pragma once
#include <vector>
#include "../../types.h"
#include "../types.h"

namespace IQVideoProcessor::Pipeline {

enum class LineDetectionEventType {
  STANDARD_DETECTED,
  SYNC_LOCKED,
  SYNC_LOCK_LOST,
  FRAME_BEGIN,
  FRAME_END,
  EQUALIZATION,
  LINE_RECEIVED,
  UNKNOWN
};

struct LineDetectionEvent {
  LineDetectionEventType type{LineDetectionEventType::UNKNOWN};
  VideoStandard videoStandard{UNKNOWN_VIDEO_STANDARD};
  std::vector<TFloat> data;
  size_t dataSize{0};
  size_t lineNumber{0};
  size_t frameNumber{0};
  bool odd{false};
};

} // namespace IQVideoProcessor::Pipeline

