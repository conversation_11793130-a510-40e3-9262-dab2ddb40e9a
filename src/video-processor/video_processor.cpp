#include <algorithm>
#include "./video_processor.h"
#include "./video_processor_configs.h"

class IIQStream;
namespace IQVideoProcessor {

VideoProcessor::VideoProcessor(std::unique_ptr<IIQStream> stream, std::function<void()> onStateChanged)
  : stream_(std::move(stream)),
    onStateChanged_(std::move(onStateChanged)
) {

}

VideoProcessor::~VideoProcessor() {
  stop();
}

bool VideoProcessor::start() {
  if (running_.load()) return false;

  // Initialize the pipeline components
  if (!initialize()) return false;

  // Create the main pipeline processing thread (demodulation->line detection->frame composition->output queue)
  pipelineMainThread_ = std::make_unique<std::thread>([this]() {
    pipelineThreadRunning_.store(true, std::memory_order_release);
    {
      std::unique_lock lock(threadsMutex_);
      threadsCv_.notify_one(); // wake acquisition thread
    }
    // Main processing loop
    while (running_.load()) {
      if (!acquisitionBridge_->tick()) break;
    }
    running_.store(false);
  });

  // Creating the acquisition thread that consumes data from the IQ stream (as fast as possible)
  acquisitionThread_ = std::make_unique<std::thread>([this]() {
    {
      std::unique_lock lock(threadsMutex_);
      threadsCv_.wait(lock, [this] {
        return pipelineThreadRunning_.load(std::memory_order_acquire);
      });
    }
    // Main acquisition loop
    while (running_.load()) {
      if (!acquisitionNode_->tick()) break;
    }
    running_.store(false);
  });

  if (!pipelineMainThread_ || !acquisitionThread_) {
    return false;
  }

  running_.store(true);
  return true;
}

void VideoProcessor::stop() {
  if (!running_.load()) return;
  running_.store(false);

  // Signal all pipeline nodes to stop and exit their threads, then join the threads
  sendStopToPipeline();
  if (acquisitionThread_ && acquisitionThread_->joinable()) acquisitionThread_->join();
  if (pipelineMainThread_ && pipelineMainThread_->joinable()) pipelineMainThread_->join();

  deinitialize();
}

bool VideoProcessor::hasNextFrame() const {
  return outputQueueNode_ && outputQueueNode_->hasNextFrame();
}

Pipeline::FrameCompositionResult& VideoProcessor::getNextFrame() const {
  return outputQueueNode_->getNextFrame();
}

bool VideoProcessor::initialize() {
  if (!stream_) return false;

  sampleRate_ = stream_->sampleRate();
  if (sampleRate_ == 0) return false;

  // Calculating the maximum samples per video line, required to pre-allocate buffers and other computations
  maxVideoLineSamples_ = static_cast<size_t>(static_cast<TFloat>(sampleRate_) / MIN_LINE_RATE_HZ);
  if (maxVideoLineSamples_ < MIN_SAMPLES_PER_VIDEO_LINE) return false;

  // Calculate the desired amount of processable samples and overlap per window
  auto effectiveSamples = static_cast<size_t>(static_cast<TFloat>(maxVideoLineSamples_) * LINES_PER_CHUNK); // ~100 lines
  auto leftOverlap = maxVideoLineSamples_ * 8; // 8 lines overlap on each side, to ensure we catch non-standard VSync pulses

  // Create pipeline components using pointer-based storage
  // Note: stream_ is moved to acquisitionNode_, so it becomes nullptr after this
  acquisitionNode_ = std::make_unique<Pipeline::IQAcquisitionNode>(std::move(stream_), IQ_STREAM_READ_SIZE, effectiveSamples, leftOverlap);
  acquisitionBridge_ = std::make_unique<Pipeline::IQAcquisitionBridge>(100);
  demodNode_ = std::make_unique<Pipeline::IQDemodulationNode>();
  demodPassthrough_ = std::make_unique<Pipeline::IQDemodulationLink>();
  lineDetectionNode_ = std::make_unique<Pipeline::LineDetectionNode>(sampleRate_);
  lineDetectionPassthrough_ = std::make_unique<Pipeline::LineDetectionLink>();
  frameCompositionNode_ = std::make_unique<Pipeline::FrameCompositionNode>(sampleRate_);
  frameCompositionPassthrough_ = std::make_unique<Pipeline::FrameCompositionLink>();
  outputQueueNode_ = std::make_unique<Pipeline::OutputQueueNode>(30, [this] () {
    if (!running_.load()) return false;
    onStateChanged_();
    return true;
  });

  connectPipelineNodes();

  return true;
}

void VideoProcessor::connectPipelineNodes() {
  // Acquisition node reads from the stream
  acquisitionNode_->sendDataTo(acquisitionBridge_.get());

  demodNode_->receiveDataFrom(acquisitionBridge_.get());
  demodNode_->sendDataTo(demodPassthrough_.get());

  lineDetectionNode_->receiveDataFrom(demodPassthrough_.get());
  lineDetectionNode_->sendDataTo(lineDetectionPassthrough_.get());

  frameCompositionNode_->receiveDataFrom(lineDetectionPassthrough_.get());
  frameCompositionNode_->sendDataTo(frameCompositionPassthrough_.get());

  outputQueueNode_->receiveDataFrom(frameCompositionPassthrough_.get());
}

void VideoProcessor::deinitialize() {
  acquisitionNode_.reset();
  acquisitionBridge_.reset();
  demodNode_.reset();
  demodPassthrough_.reset();
  lineDetectionNode_.reset();
  lineDetectionPassthrough_.reset();
  frameCompositionNode_.reset();
  frameCompositionPassthrough_.reset();
  outputQueueNode_.reset();
  acquisitionThread_.reset();
  pipelineMainThread_.reset();
  stream_.reset();
  sampleRate_ = 0;
  maxVideoLineSamples_ = 0;
}

void VideoProcessor::sendStopToPipeline() const {
  if (acquisitionNode_) acquisitionNode_->stop();
  if (acquisitionBridge_) acquisitionBridge_->stop();
  if (demodNode_) demodNode_->stop();
  if (demodPassthrough_) demodPassthrough_->stop();
  if (lineDetectionNode_) lineDetectionNode_->stop();
  if (lineDetectionPassthrough_) lineDetectionPassthrough_->stop();
  if (frameCompositionNode_) frameCompositionNode_->stop();
  if (frameCompositionPassthrough_) frameCompositionPassthrough_->stop();
  if (outputQueueNode_) outputQueueNode_->stop();
}

} // namespace IQVideoProcessor
