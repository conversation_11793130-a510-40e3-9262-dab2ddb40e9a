#pragma once

#include <uv.h>
#include <v8.h>

#include <atomic>
#include <deque>
#include <functional>
#include <mutex>

namespace NodeHelpers {

// Posts C++ lambdas from arbitrary threads to execute on Node's main thread.
// Each task runs inside a V8 HandleScope and the current V8 Context is made current.
class NodeThreadRunner {
public:
  using Task = std::function<void(v8::Isolate*, v8::Local<v8::Context>)>;

  // If loop is nullptr, enable() will use uv_default_loop().
  explicit NodeThreadRunner(v8::Isolate* isolate, uv_loop_t* loop = nullptr) noexcept;

  NodeThreadRunner(const NodeThreadRunner&) = delete;
  NodeThreadRunner& operator=(const NodeThreadRunner&) = delete;
  NodeThreadRunner(NodeThreadRunner&&) = delete;
  NodeThreadRunner& operator=(NodeThreadRunner&&) = delete;

  ~NodeThreadRunner();

  // Initializes the uv_async handle. Idempotent; safe to call multiple times.
  // Returns true on success.
  bool enable();

  // Stops accepting new tasks, clears pending tasks, and closes the uv handle.
  // Safe to call multiple times and from any thread.
  void disable();

  // Enqueues a task to run on Node's thread. Returns false if not enabled.
  // Thread-safe; callable from any thread.
  bool run(Task task);

  // Returns whether the runner is currently enabled (accepting tasks).
  bool isEnabled() const noexcept { return enabled_.load(std::memory_order_acquire); }

private:
  static void uvCallback(uv_async_t* handle) noexcept;
  static void onHandleClosed(uv_handle_t* handle) noexcept;

  void drain(); // Runs queued tasks on Node's thread.

  v8::Isolate* isolate_;
  uv_loop_t* loop_;             // Set in ctor or enable(); not thread-safe to mutate without lock.
  uv_async_t* handle_{nullptr}; // Owned; deleted in onHandleClosed().

  mutable std::mutex mtx_;      // Protects queue_ and handle_ send/close sequencing.
  std::deque<Task> queue_;      // Pending tasks.

  std::atomic<bool> enabled_{false};
};

}
