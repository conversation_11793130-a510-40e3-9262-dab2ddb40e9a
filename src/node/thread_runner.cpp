#include "./thread_runner.h"
#include <utility>

namespace NodeHelpers {

NodeThreadRunner::NodeThreadRunner(v8::Isolate* isolate, uv_loop_t* loop) noexcept : isolate_(isolate), loop_(loop) {}

NodeThreadRunner::~NodeThreadRunner() {
  disable();
}

bool NodeThreadRunner::enable() {
  // Fast path: avoid re-initialization if already enabled.
  bool expected = false;
  if (!enabled_.compare_exchange_strong(expected, true, std::memory_order_acq_rel)) {
    return true; // Already enabled
  }

  // Create and initialize the uv_async handle.
  uv_async_t* h = new uv_async_t();
  h->data = this;

  uv_loop_t* targetLoop = loop_ ? loop_ : uv_default_loop();

  const int rc = uv_async_init(targetLoop, h, &NodeThreadRunner::uvCallback);
  if (rc != 0) {
    // Failed; revert enabled state and clean up.
    enabled_.store(false, std::memory_order_release);
    delete h;
    return false;
  }

  // Publish the handle under lock to serialize with disable()/uv_close().
  {
    std::lock_guard<std::mutex> lk(mtx_);
    handle_ = h;
    if (!loop_) {
      loop_ = targetLoop;
    }
  }

  return true;
}

void NodeThreadRunner::disable() {
  // Stop accepting tasks immediately.
  const bool wasEnabled = enabled_.exchange(false, std::memory_order_acq_rel);
  if (!wasEnabled) {
    return; // Already disabled
  }

  uv_async_t* hToClose = nullptr;

  {
    std::lock_guard<std::mutex> lk(mtx_);
    // Drop all queued tasks to avoid running code after teardown.
    queue_.clear();

    // Close the handle if present. We null it here so run() won't try to send,
    // and the memory is freed in onHandleClosed().
    hToClose = handle_;
    handle_ = nullptr;
  }

  if (hToClose) {
    uv_close(reinterpret_cast<uv_handle_t*>(hToClose), &NodeThreadRunner::onHandleClosed);
  }
}

bool NodeThreadRunner::run(Task task) {
  if (!enabled_.load(std::memory_order_acquire)) {
    return false;
  }

  uv_async_t* h = nullptr;

  {
    // Serialize enqueue + send with disable()/uv_close() to avoid races.
    std::lock_guard<std::mutex> lk(mtx_);

    if (!enabled_.load(std::memory_order_relaxed) || handle_ == nullptr) {
      return false;
    }

    queue_.emplace_back(std::move(task));
    h = handle_;
  }

  // Send outside the lock to reduce contention; handle_ is stable here
  // because disable() cannot close it until it acquires the same mutex.
  uv_async_send(h);
  return true;
}

void NodeThreadRunner::uvCallback(uv_async_t* handle) noexcept {
  auto* self = static_cast<NodeThreadRunner*>(handle->data);
  if (self) {
    self->drain();
  }
}

void NodeThreadRunner::onHandleClosed(uv_handle_t* handle) noexcept {
  // The uv_async_t was allocated with new; delete it now that libuv has closed it.
  delete reinterpret_cast<uv_async_t*>(handle);
}

void NodeThreadRunner::drain() {
  // If disabled, there should be nothing to do (queue was cleared), but
  // we allow draining if any tasks slipped through before disable().
  std::deque<Task> tasks;

  {
    std::lock_guard<std::mutex> lk(mtx_);
    if (!queue_.empty()) {
      tasks.swap(queue_);
    }
  }

  if (tasks.empty()) {
    return;
  }

  // Enter V8 scopes on the Node thread.
  v8::HandleScope hs(isolate_);
  v8::Local<v8::Context> context = isolate_->GetCurrentContext();
  v8::Context::Scope cs(context);

  for (auto& t : tasks) {
    if (t) {
      // Never throw across V8/native boundary; tasks should swallow exceptions.
      try {
        t(isolate_, context);
      } catch (...) {
        // Swallow to preserve process integrity.
      }
    }
  }
}

}
