#pragma once
#include "logging.h"
#include <iostream>

namespace Logging::detail {

// Choose target stream per level (adjust if you want all to go to std::cout)
inline std::ostream& select_stream(LogLevel lvl) {
    switch (lvl) {
        case LogLevel::WARNING: return std::cerr;
        case LogLevel::VERBOSE: return std::cout;
        default:                return std::cout;
    }
}

// RAII stream that adds a newline on destruction
class LogLine {
    std::ostream* os_;
public:
    explicit LogLine(std::ostream* os) : os_(os) {}
    ~LogLine() { if (os_) (*os_) << std::endl; }

    template <typename T>
    LogLine& operator<<(const T& v) { if (os_) (*os_) << v; return *this; }

    // Support for manipulators like std::hex, std::setw, std::endl
    using Manip = std::ostream& (*)(std::ostream&);
    LogLine& operator<<(Manip m) { if (os_) (*os_) << m; return *this; }
};

// Construct a LogLine only if enabled; otherwise return a "null" sink
inline LogLine make_logline(LoggerType logger, LogLevel level) {
    if (!isLogLevelEnabled(logger, level)) return LogLine(nullptr);
    return LogLine(&select_stream(level));
}

} // namespace Logging::detail

// Block guard: run the block only if the level is enabled for the logger.
#define LOG_IF(logger, level) \
    if (Logging::isLogLevelEnabled((logger), (level)))

// Stream-style logger:
// Usage: LOG(LoggerType::GENERAL, Logging::LogLevel::WARNING) << "Hello " << x;
#define LOG(logger, level) \
    if (!Logging::isLogLevelEnabled((logger), (level))) ; \
    else Logging::detail::make_logline((logger), (level))

// Convenience for common levels
#define LOG_WARN(logger)    LOG((logger), Logging::LogLevel::WARNING)
#define LOG_VERBOSE(logger) LOG((logger), Logging::LogLevel::VERBOSE)

#define LOG_WARN_IF(logger)    LOG_IF((logger), Logging::LogLevel::WARNING)
#define LOG_VERBOSE_IF(logger) LOG_IF((logger), Logging::LogLevel::VERBOSE)

// One-shot macro that takes a stream expression and appends newline:
// Usage: LOG_MSG(LoggerType::GENERAL, Logging::LogLevel::WARNING, "x=" << x);
#define LOG_MSG(logger, level, expr) \
    do { \
        if (Logging::isLogLevelEnabled((logger), (level))) { \
            Logging::detail::select_stream((level)) << expr << std::endl; \
        } \
    } while (0)

// Debug-only variants (compiled out in Release when NDEBUG is defined)
#ifdef NDEBUG
  #define DLOG_IF(logger, level) if (false)
  #define DLOG(logger, level)    if (true) ; else Logging::detail::make_logline((logger), (level))
  #define DLOG_MSG(logger, level, expr) do {} while (0)
#else
  #define DLOG_IF(logger, level) LOG_IF((logger), (level))
  #define DLOG(logger, level)    LOG((logger), (level))
  #define DLOG_MSG(logger, level, expr) LOG_MSG((logger), (level), expr)
#endif
